{"name": "bitnova-signer", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@rainbow-me/rainbowkit": "^2.2.8", "@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "@tanstack/react-query": "^5.81.5", "@wagmi/core": "^2.17.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "ethers": "^6.15.0", "isbot": "^5.1.27", "ky": "^1.8.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.5.3", "siwe": "^3.0.0", "tailwind-merge": "^3.3.1", "viem": "2.x", "wagmi": "^2.15.6"}, "devDependencies": {"@react-router/dev": "^7.5.3", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "vite": "^7.0.2", "vite-tsconfig-paths": "^5.1.4"}, "pnpm": {"patchedDependencies": {}}, "resolutions": {"@vanilla-extract/sprinkles": "1.6.5", "@vanilla-extract/dynamic": "2.1.5"}}