import { GalleryVerticalEnd } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ConnectButton, useConnectModal } from '@rainbow-me/rainbowkit';
import { useAccount, useAccountEffect, useConfig, useDisconnect } from 'wagmi';
import { SiweMessage } from 'siwe';
import ky from 'ky';
import { getEnsName, signMessage } from '@wagmi/core';
import type { Address, Chain } from 'viem';
import { useMutation } from '@tanstack/react-query';
import { btrTestnet } from 'viem/chains';
import { useNavigate } from 'react-router';

type SignInParams = {
  address: Address;
};

function useSignIn() {
  const config = useConfig();

  const mutation = useMutation({
    mutationFn: async ({ address }: SignInParams) => {
      const ens = getEnsName(config, { address });
      const { nonce } = await ky.get('/api/nonce').json<{ nonce: string }>();

      const siweMessage = new SiweMessage({
        domain: window.location.host,
        address,
        statement: 'Sign in to BitNova Signer',
        uri: window.location.origin,
        version: '1',
        chainId: btrTestnet.id,
        nonce,
      });

      const signature = await signMessage(config, { message: siweMessage.prepareMessage() });

      await ky.post('/api/sign-in', {
        json: {
          message: siweMessage.prepareMessage(),
          ens,
          signature,
        },
      });
    },
  });

  return {
    signIn: mutation.mutate,
    signInAsync: mutation.mutateAsync,
    ...mutation,
  };
}

export function LoginForm({ className, ...props }: React.ComponentProps<'div'>) {
  const { openConnectModal } = useConnectModal();
  const { disconnect } = useDisconnect();
  const { address } = useAccount();
  const navigate = useNavigate();

  const handleConnectWallet = () => {
    openConnectModal?.();
  };

  const handleSignIn = ({ address }: SignInParams) => {
    signIn(
      { address },
      {
        onSuccess() {
          navigate('/');
        },
        onError(error) {
          console.error(error);
          disconnect();
        },
      },
    );
  };

  const { signIn, isPending } = useSignIn();

  useAccountEffect({
    onConnect({ address }) {
      handleSignIn({ address });
    },
  });

  const renderActionButton = () => {
    if (!address) {
      return (
        <Button
          className="w-full cursor-pointer"
          onClick={handleConnectWallet}
          disabled={isPending}
        >
          Connect Wallet
        </Button>
      );
    }

    return (
      <Button
        className="w-full cursor-pointer"
        onClick={() => handleSignIn({ address })}
        disabled={isPending}
      >
        Sign In
      </Button>
    );
  };

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <div className="flex flex-col gap-6">
        <div className="flex flex-col items-center gap-2">
          <a href="#" className="flex flex-col items-center gap-2 font-medium">
            <div className="flex size-8 items-center justify-center rounded-md">
              <GalleryVerticalEnd className="size-6" />
            </div>
            <span className="sr-only">BitNova Dashboard</span>
          </a>
          <h1 className="text-xl font-bold">Welcome to BitNova Dashboard</h1>
          <p className="text-muted-foreground text-center text-sm">
            Connect your wallet to get started
          </p>
        </div>
        <div className="flex flex-col gap-6">
          {renderActionButton()}
          <ConnectButton />
        </div>
      </div>
      <div className="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4">
        By clicking continue, you agree to our <a href="#">Terms of Service</a> and{' '}
        <a href="#">Privacy Policy</a>.
      </div>
    </div>
  );
}
