import { createContext, useContext } from 'react';
import type { Address } from 'viem';

type SessionContextData = {
  address?: Address;
};

const sessionContext = createContext<SessionContextData>({});

type SessionProviderProps = {
  address?: Address;
  children: React.ReactNode;
};

export function SessionProvider({ address, children }: SessionProviderProps) {
  return <sessionContext.Provider value={{ address }}>{children}</sessionContext.Provider>;
}

export function useSession() {
  return useContext(sessionContext);
}
