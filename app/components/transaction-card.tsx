import type { Transaction } from '@/lib/api';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from './ui/card';

type TransactionCardProps = {
  transaction: Transaction;
};

export function TransactionCard({ transaction }: TransactionCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="uppercase">{transaction.type}</CardTitle>
        <CardDescription>{transaction.hash}</CardDescription>
      </CardHeader>
      <CardContent className="text-xs space-y-0.5">
        <h3>{transaction.hash}</h3>
        <TransactionField label="From" value={transaction.from_address} />
        <TransactionField label="Original Amount" value={transaction.original_amount} />
        <TransactionField label="Amount" value={transaction.amount} />
        <TransactionField label="Fee" value={transaction.fee} />
        <TransactionStatus status={transaction.status} />
        <TransactionField label="Created At" value={transaction.created_at} />
        <TransactionField label="Updated At" value={transaction.updated_at} />
        <TransactionField label="Current Process" value={transaction.current_process} />
        <TransactionField label="Total Process" value={transaction.total_process} />
        <TransactionField label="Type" value={transaction.type} />
      </CardContent>
      <CardFooter>
        <p>Transaction footer</p>
      </CardFooter>
    </Card>
  );
}

function TransactionStatus({ status }: { status: Transaction['status'] }) {
  return <p>{status}</p>;
}

function TransactionField({ label, value }: { label: string; value: React. }) {
  return (
    <p>
      <span className="font-bold">{label}:</span> {value}
    </p>
  );
}
