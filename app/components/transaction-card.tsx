import type { Transaction } from '@/lib/api';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from './ui/card';

type TransactionCardProps = {
  transaction: Transaction;
};

export function TransactionCard({ transaction }: TransactionCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="uppercase">{transaction.type}</CardTitle>
        <CardDescription>{transaction.hash}</CardDescription>
      </CardHeader>
      <CardContent className="text-xs space-y-0.5">
        <h3>{transaction.hash}</h3>
        <p>From: {transaction.from_address}</p>
        <p>Original Amount: {transaction.original_amount}</p>
        <p>Amount: {transaction.amount}</p>
        <p>Fee: {transaction.fee}</p>
        <p>{transaction.status}</p>
        <p>{transaction.created_at}</p>
        <p>{transaction.updated_at}</p>
        <p>{transaction.current_process}</p>
        <p>{transaction.total_process}</p>
        <p>{transaction.type}</p>
      </CardContent>
      <CardFooter>
        <p>Transaction footer</p>
      </CardFooter>
    </Card>
  );
}

function TransactionStatus({ status }: { status: Transaction['status'] }) {
  return <p>{status}</p>;
}

function TransactionField({ label, value }: { label: string; value: string | number }) {
  return (
    <p>
      <span className="font-bold">{label}:</span> {value}
    </p>
  );
}
