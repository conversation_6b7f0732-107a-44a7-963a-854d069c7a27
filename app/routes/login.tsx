import { LoginForm } from '@/components/login-form';
import { getSession } from '@/session.server';
import { redirect, type LoaderFunctionArgs } from 'react-router';

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request.headers.get('<PERSON>ie'));

  const address = session.get('address');
  if (address) {
    return redirect('/');
  }

  return {};
}

export default function LoginPage() {
  return (
    <div className="bg-background flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10">
      <div className="w-full max-w-sm">
        <LoginForm />
      </div>
    </div>
  );
}
