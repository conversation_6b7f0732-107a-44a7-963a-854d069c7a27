import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export type ShortenAddressOptions = {
  prefixLength?: number;
  suffixLength?: number;
};

export function shortenAddress(address: string, options: ShortenAddressOptions = {}) {
  const { prefixLength = 6, suffixLength = 4 } = options;
  return `${address.slice(0, prefixLength)}...${address.slice(-suffixLength)}`;
}
