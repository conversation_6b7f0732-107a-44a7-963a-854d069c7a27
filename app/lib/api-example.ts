import { getAPIClient } from './api';

// Example usage of the APIClient
export async function exampleUsage() {
  // Get the API client instance
  const apiClient = getAPIClient('https://your-api-base-url.com');

  try {
    // 1. Get committee list
    const committees = await apiClient.getCommittees();
    console.log('Committees:', committees.data.list);

    // 2. Get transaction list for an address
    const transactions = await apiClient.getTransactionList({
      address: '0x9c4d23fa4891160c6a734487d0df87919a7daaa926e65ed577887e0cd55054ef'
    });
    console.log('Transactions:', transactions.data);

    // 3. Submit a transaction signature
    const signResult = await apiClient.signTransaction({
      hash: '0x1234567890abcdef',
      signature: '0xabcdef1234567890',
      type: 'withdraw'
    });
    console.log('Sign result:', signResult.data);

    // 4. Get specific transaction details
    const transactionDetails = await apiClient.getTransaction({
      hash: '4TYr7WLD2PDi4divFES1Bc2oSxxoVTXXgh2nTigNwZXr',
      type: 'withdraw',
      address: '0x9c4d23fa4891160c6a734487d0df87919a7daaa926e65ed577887e0cd55054ef'
    });
    console.log('Transaction details:', transactionDetails.data);

  } catch (error) {
    console.error('API Error:', error);
  }
}
