import { type RouteConfig, index, layout, prefix, route } from '@react-router/dev/routes';

export default [
  route('login', 'routes/login.tsx'),
  layout('routes/dashboard/layout.tsx', [
    index('routes/dashboard/index.tsx'),
    route('signer/transactions', 'routes/dashboard/transactions.tsx'),
  ]),
  ...prefix('api', [
    route('nonce', 'routes/api/nonce.tsx'),
    route('sign-in', 'routes/api/sign-in.tsx'),
    route('sign-out', 'routes/api/sign-out.tsx'),
  ]),
] satisfies RouteConfig;
