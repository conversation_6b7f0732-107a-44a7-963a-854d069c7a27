---
title: bitlayer
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# bitlayer

Base URLs:

# Authentication

# bitnova

## GET 委员会列表

GET /bitnova/committees

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "data": {
    "list": [
      "0x9f0311F1e35e312d0F4B79Dcf5C1a936b8AE0771"
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» list|[string]|true|none||none|

## GET 交易列表

GET /bitnova/transaction/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|address|query|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "data": [
    {
      "hash": "4TYr7WLD2PDi4divFES1Bc2oSxxoVTXXgh2nTigNwZXr",
      "from_address": "0x9c4d23fa4891160c6a734487d0df87919a7daaa926e65ed577887e0cd55054ef",
      "original_amount": 1098900,
      "amount": 1097802,
      "fee": 1098,
      "status": "new",
      "created_at": 1751585793,
      "updated_at": 1751585848,
      "current_process": 0,
      "total_process": 3,
      "type": "withdraw"
    },
    {
      "hash": "0x1006e4f6422f90a02cd14ef4e916b667b5cbed2faccc5dde0e90b0cbca35b4c9",
      "from_address": "0x8e9FC1743b3FD7D79a9448a851Fe6F4d7073c610",
      "original_amount": 1100000,
      "amount": 1098900,
      "fee": 1100,
      "status": "new",
      "created_at": 1751584654,
      "updated_at": 1751584707,
      "current_process": 0,
      "total_process": 3,
      "type": "deposit"
    },
    {
      "hash": "0x683f357cc0d95e0c354c97d6e1d752bd14d625649f334773a0503c39a54bd4ae",
      "from_address": "0x8e9FC1743b3FD7D79a9448a851Fe6F4d7073c610",
      "original_amount": 1000000,
      "amount": 999000,
      "fee": 1000,
      "status": "new",
      "created_at": 1751508220,
      "updated_at": 1751566780,
      "current_process": 0,
      "total_process": 3,
      "type": "deposit"
    },
    {
      "hash": "BKBQM3feJijXU6BafXAag2Sf3H3264yMH6cufmPmaY6f",
      "from_address": "0x262c9b50aa6ee69473b814150e057552bbf5c5578e33c4aa0d6e8659437349d8",
      "original_amount": 1000000,
      "amount": 998000,
      "fee": 2000,
      "status": "new",
      "created_at": 1751481992,
      "updated_at": 1751481992,
      "current_process": 0,
      "total_process": 3,
      "type": "withdraw"
    },
    {
      "hash": "8T5w948LftTavDksFEn5cgL6NSF3xWt9HcPTgSP1LsCt",
      "from_address": "0x262c9b50aa6ee69473b814150e057552bbf5c5578e33c4aa0d6e8659437349d8",
      "original_amount": 1900000,
      "amount": 1896200,
      "fee": 3800,
      "status": "new",
      "created_at": 1751481992,
      "updated_at": 1751481992,
      "current_process": 0,
      "total_process": 3,
      "type": "withdraw"
    },
    {
      "hash": "B8SxYFMrvN9JaJFKU9rFhonwGXa4dGsFE1nLFvwJ1bMy",
      "from_address": "0x54321a86bcbabfe8760bc8ecb433915349d43f722945da2b2bede020774608d8",
      "original_amount": 900000,
      "amount": 898200,
      "fee": 1800,
      "status": "new",
      "created_at": 1751481992,
      "updated_at": 1751481992,
      "current_process": 0,
      "total_process": 3,
      "type": "withdraw"
    },
    {
      "hash": "7opcXQF7s5dTLvEt516MqaoNnDZQZQVEdTeBm8dseCSb",
      "from_address": "0x675e1e18a302ff8c79b9eb9edc43effbe10a3efae2e4aa54162cdad1af5e5c61",
      "original_amount": 159000000,
      "amount": 158682000,
      "fee": 318000,
      "status": "new",
      "created_at": 1751481992,
      "updated_at": 1751481992,
      "current_process": 0,
      "total_process": 3,
      "type": "withdraw"
    },
    {
      "hash": "4hCc47xsnY42nrhd363yxAwGWk5KdG5q6JEHRUfjRsHm",
      "from_address": "0x675e1e18a302ff8c79b9eb9edc43effbe10a3efae2e4aa54162cdad1af5e5c61",
      "original_amount": 20000000,
      "amount": 19960000,
      "fee": 40000,
      "status": "new",
      "created_at": 1751481992,
      "updated_at": 1751481992,
      "current_process": 0,
      "total_process": 3,
      "type": "withdraw"
    },
    {
      "hash": "7duPgn8uChiQ845gYQh6LaHnTTDsQajVMQDAJahyWgcJ",
      "from_address": "0x675e1e18a302ff8c79b9eb9edc43effbe10a3efae2e4aa54162cdad1af5e5c61",
      "original_amount": 30000000,
      "amount": 29940000,
      "fee": 60000,
      "status": "new",
      "created_at": 1751481992,
      "updated_at": 1751481992,
      "current_process": 0,
      "total_process": 3,
      "type": "withdraw"
    },
    {
      "hash": "FzyeV4w3qMwz7DzQNTQ8WsRJX6wjhREKGdL1ercBs8tc",
      "from_address": "0x675e1e18a302ff8c79b9eb9edc43effbe10a3efae2e4aa54162cdad1af5e5c61",
      "original_amount": 50000000,
      "amount": 49900000,
      "fee": 100000,
      "status": "new",
      "created_at": 1751481992,
      "updated_at": 1751481992,
      "current_process": 0,
      "total_process": 3,
      "type": "withdraw"
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» data|[object]|true|none||none|
|»» hash|string|true|none||none|
|»» from_address|string|true|none||none|
|»» original_amount|integer|true|none||none|
|»» amount|integer|true|none||none|
|»» fee|integer|true|none||none|
|»» status|string|true|none||none|
|»» created_at|integer|true|none||none|
|»» updated_at|integer|true|none||none|
|»» current_process|integer|true|none||none|
|»» total_process|integer|true|none||none|
|»» type|string|true|none||none|

#### 枚举值

|属性|值|
|---|---|
|status|new|
|status|signed|
|status|finished|
|status|failed|
|type|withdraw|
|type|deposit|

## POST 提交签名

POST /bitnova/transaction/sign

> Body 请求参数

```json
{
  "hash": "0x",
  "signature": "0x",
  "type": "withdraw"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» hash|body|string| 是 |none|
|» signature|body|string| 是 |none|
|» type|body|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "data": {
    "list": [
      "0x9f0311F1e35e312d0F4B79Dcf5C1a936b8AE0771"
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» data|[object]|true|none||none|
|»» hash|string|true|none||none|
|»» from_address|string|true|none||none|
|»» original_amount|integer|true|none||none|
|»» amount|integer|true|none||none|
|»» fee|integer|true|none||none|
|»» status|string|true|none||none|
|»» created_at|integer|true|none||none|
|»» updated_at|integer|true|none||none|
|»» current_process|integer|true|none||none|
|»» total_process|integer|true|none||none|
|»» type|string|true|none||none|

## GET 交易详情

GET /bitnova/transaction/get_one

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|hash|query|string| 是 |none|
|type|query|string| 是 |none|
|address|query|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "data": {
    "hash": "4TYr7WLD2PDi4divFES1Bc2oSxxoVTXXgh2nTigNwZXr",
    "from_address": "0x9c4d23fa4891160c6a734487d0df87919a7daaa926e65ed577887e0cd55054ef",
    "original_amount": 1098900,
    "amount": 1097802,
    "fee": 1098,
    "status": "finished",
    "created_at": 1751585793,
    "updated_at": 1751585848,
    "current_process": 3,
    "total_process": 3,
    "type": "withdraw"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» hash|string|true|none||none|
|»» from_address|string|true|none||none|
|»» original_amount|integer|true|none||none|
|»» amount|integer|true|none||none|
|»» fee|integer|true|none||none|
|»» status|string|true|none||none|
|»» created_at|integer|true|none||none|
|»» updated_at|integer|true|none||none|
|»» current_process|integer|true|none||none|
|»» total_process|integer|true|none||none|
|»» type|string|true|none||none|

# 数据模型

